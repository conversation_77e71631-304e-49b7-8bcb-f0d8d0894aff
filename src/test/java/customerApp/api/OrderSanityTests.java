package customerApp.api;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import io.restassured.module.jsv.JsonSchemaValidator;
import org.testng.Assert;
import org.testng.annotations.Test;
import java.time.Duration;
import java.util.Collections;
import java.util.Random;

public class OrderSanityTests extends BaseTest {

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithBackToWalletFixedWithTippingDuringRatingAndPartialPayFromWalletAndCollectedAmountGreaterThanOrderAmount() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Create Back to wallet coupon with fixed value
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createCouponUsingCouponCode(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "back to wallet",
                "supermarket",
                "commercial",
                100,
                0,
                0,
                "active",
                true,
                false,
                false,
                "now"));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update random user balance to 1
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("1.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , defaultTestData.get().getTestCoupon().getCouponCode()
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false, false,false,""));

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("cod","Cash On Delivery"
                ,1,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "processing" ,true);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , true , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , true , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);
        //Asserting Coupon Object
        createOrderApiValidator.get().assertCouponObjectInResponse(
                defaultTestData.get().getTestCoupon().getCouponCode()
                , defaultTestData.get().getRandomTestUser().getTestOrder());

        //Complete order cycle through the apis
        //Setting collected amount = order amount -1(balance pre-existing) + 6(extra collected amount, for gratuity)
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get()
                , (defaultTestData.get().getRandomTestUser().getTestOrder().getTotal() - 1) + 6
                , false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                        .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Create Gratuity during Rating order
        orderPaymentApiClient.get().createGratuityInRatingUsingApi(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder(), "wallet");

        //Wait for 120 secs until back to wallet reflects on balance of user
        Thread.sleep(Duration.ofMinutes(5));

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(
                                defaultTestData.get().getRandomTestUser().getId()
                                , defaultTestData.get().getAdminUser())
                        .getCurrentBalance());

        //Assert current balance = 100(coupon value) + 6(extra collected amount) - 5(gratuity)
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                101, String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithPartialPayFromWalletAndCollectedAmountLessThanOrderAmount() {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update random user balance to 5
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("1.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false, false,false,""));

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("cod","Cash On Delivery"
                ,1,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "processing" ,true);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);

        //Complete order cycle through the apis , setting collected amount to -2
        // -2 = -1 from balance, and -1 to be less than the total
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal() - 2,false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                        .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert current balance = -20
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                -1, String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithTippingDuringCheckoutAndFullyPayFromWalletAndMarkOrderAsFree() {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update random user balance to 500
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("500.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "10"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false, false,false,""));

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("cod","Balance"
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                ,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "processing" ,true);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);

        //Complete order cycle through the apis , setting mark order as free
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(),0.0f,true);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                        .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert current balance = 500
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                500
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithBackToWalletPercentageWithTippingDuringCheckoutAndFullyPayFromWalletAndCollectedAmountGreaterThanOrderAmount() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Create Back to wallet coupon with percentage value
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createCouponUsingCouponCode(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "back to wallet",
                "supermarket",
                "commercial",
                10,
                0,
                0,
                "active",
                true,
                false,
                true,
                "now"));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update random user balance to 1000
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("500.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "1"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , defaultTestData.get().getTestCoupon().getCouponCode()
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false, false,false,""));

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("cod","Balance"
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                ,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "processing" ,true);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , true , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , true , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);
        //Asserting Gratuity Object
        createOrderApiValidator.get().assertGratuityObjectInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , "1" , "1");
        //Asserting Coupon Object
        createOrderApiValidator.get().assertCouponObjectInResponse(
                defaultTestData.get().getTestCoupon().getCouponCode()
                , defaultTestData.get().getRandomTestUser().getTestOrder());

        //Complete order cycle through the apis , setting collected amount to value > order total by 20
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get()
                ,1.0f,false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                        .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Wait for 120 secs until back to wallet reflects on balance of user
        Thread.sleep(Duration.ofMinutes(5));

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance()
                , "Current user balance is null and it shouldn't be");

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getTestOrder().getTotalWithGratuity()
                , String.format("Total with Gratuity is null for Order ID: %s"
                        , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        // Verify that the user's current balance is updated correctly after the Coupon is reflected in wallet
        Assert.assertEquals(
                // Expected balance after coupon reflects to the nearest two decimals
                String.format("%.2f", Double.parseDouble(defaultTestData.get().getRandomTestUser().getCurrentBalance())),
                // Calculated balance after coupon reflects to the nearest two decimals
                String.format("%.2f", (
                        // Initial balance
                        500.0
                                // Subtract total with gratuity
                                - defaultTestData.get().getRandomTestUser().getTestOrder().getTotalWithGratuity()
                                // Add 1.0 LE (Collected Amount Added when completing the order)
                                + 1.0
                                // Add Coupon value (10% of the cart value)
                                + (defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                                - defaultTestData.get().getRandomTestUser().getTestOrder().getDeliveryFees()
                                - defaultTestData.get().getRandomTestUser().getTestOrder().getServiceFeesInFeesObject()) * 0.1)),
                // Error message with order ID
                String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId())
        );
    }

    @Test(groups = {"order-smoke","testsenddatatodatadog"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createGiftOrderWithTippingDuringCheckout() {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getBundleOnlyProducts());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "1"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getBundleOnlyProducts()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false,true,""));

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getBundleOnlyProducts().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("cod","Cash On Delivery"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "processing" ,true);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getBundleOnlyProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);
        //Asserting Gift Receipt
        createOrderApiValidator.get().assertGiftReceiptKeyInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder() , true);
        //Assert Gratuity Object
        createOrderApiValidator.get().assertGratuityObjectInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , "1","0");

        //Complete order through the apis , with collected amount = order total
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotalWithGratuity(),false);

        // Assert that order is gift
        Assert.assertTrue(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().isGiftReceipt());

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get()
                        .getRandomTestUser().getId(), defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance()
                , "Current user balance is null and it shouldn't be.");

        //Assert balance = 0 and no due amount is left
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                0 , String.format("Order ID: %s",
                        defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Assert order is completed
        Assert.assertEquals(defaultTestData.get()
                        .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());
    }

    @Test(groups = {"order-smoke","regressionTestsForShops"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createScheduledOrder() {
        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, true,false,false,""));

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));
        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("cod","Cash On Delivery"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate Time Slots , delivery date & schedule keys in Create Order Response
        createOrderApiValidator.get().assertWarehouseTimeslotDeliveryDateAndScheduleKeysInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , true ,false
                , testExecutionHelper.get().getFutureTimeStamp("hh:mm a",3,1)
                , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy"));
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "processing" ,true);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);

        //Assert that Order is scheduled
        Assert.assertTrue(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().isScheduled());
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createScheduledExpressOrder() {
        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        // Update Fb capacity to current timeslot
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                Collections.singletonList(deliveryCapacityManagementApiClient.get().getAllSlotsData(
                        defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()).getFirst().getCurrentTimeslotId()),
                0);

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, true,true,false,""));

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("cod","Cash On Delivery"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate Time Slots , delivery date & schedule keys in Create Order Response
        createOrderApiValidator.get().assertWarehouseTimeslotDeliveryDateAndScheduleKeysInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , true ,true
                , testExecutionHelper.get().getFutureTimeStamp("hh:mm a",3,1)
                , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy"));
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "processing" ,true);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);

        //Assert that Order is scheduled Express
        Assert.assertTrue(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getIsScheduledExpress());
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void validateProductStockAfterCreateOrderWithDeliveryFeesAndTippingAmount() throws InterruptedException {
        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "25"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false, false,false));

        //Await product stock reflection in logs
        Thread.sleep(Duration.ofMinutes(1));

        defaultTestData.get().setProductStockLog(controlRoomV2ApiClient.get().getProductLogStock(
                defaultTestData.get().getAdminUser(),
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),
                defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock().getMysqlId(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        Assert.assertEquals(defaultTestData.get().getProductStockLog().getDelta(), -1);
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithPartiallyPaidFromBalanceAndNotReceived(){

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register & Create address for a user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to 1
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("1.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create an order CC , fully paid by wallet and set it as test order for a random test user
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false,false,false,""));

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("cod","Cash On Delivery"
                ,1,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "processing" ,true);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Mark order as not-received
        orderApiClient.get().markOrderAsNotReceived(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(), "not received");

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Sync order
        testExecutionHelper.get().syncOrder(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId());

        //Assert order status is not received
        Assert.assertEquals(defaultTestData.get()
                        .getRandomTestUser().getAllOrders().getFirst().getStatus(), "not-received"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Update test user balance with the current balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance()
                , "Current user balance is null and it shouldn't be.");

        //Assert amount paid by wallet is 0 as it is not refunded automatically
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                0
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void validateCreateOrderWithMultiProductsSchema() {
        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        orderApiClient.get().getCreateOrderWithMultipleProductsEndpointResponse(
                        defaultTestData.get().getRandomTestUser()
                        ,defaultTestData.get().getRandomTestUser().getAddress()
                        ,"0"
                        ,"10"
                        ,"cod"
                        ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        ,""
                        ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        ,testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false,false, false,"").
                then().assertThat().
                body(JsonSchemaValidator.matchesJsonSchemaInClasspath("createOrderWithMultiProductsSchema.json"));
    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void validateCreateOrderWithSingleProductSchema() {
        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        orderApiClient.get().getCreateOrderEndpointResponse(
                        defaultTestData.get().getRandomTestUser()
                        ,defaultTestData.get().getRandomTestUser().getAddress()
                        ,"0"
                        ,"10"
                        ,"cod"
                        ,defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        ,testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false, false, false).
                then().assertThat().
                body(JsonSchemaValidator.matchesJsonSchemaInClasspath("createOrderSchema.json"));
    }

}
