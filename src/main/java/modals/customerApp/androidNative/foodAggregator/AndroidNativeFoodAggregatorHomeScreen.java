package modals.customerApp.androidNative.foodAggregator;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.*;
import org.openqa.selenium.NoSuchElementException;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.time.Duration;
import java.util.*;

public class AndroidNativeFoodAggregatorHomeScreen extends BaseAndroidScreen {

    private static final Logger logger = LoggerFactory.getLogger(AndroidNativeFoodAggregatorHomeScreen.class);

    public AndroidNativeFoodAggregatorHomeScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//*[@text=\"Delivering to\"]")
    WebElement deliveringToTitle;

    @FindBy(id = "homeRestaurantsBackBtn")
    WebElement homeBackBtn;

    @FindBy(id = "homeRestaurantsSearchBtn")
    WebElement searchBtn;

    @FindBy(id = "homeRestaurantsCartBtn")
    WebElement cartIcon;

    @FindBy(id = "homeRestaurantsTitle")
    WebElement allRestaurantsTitle;

    @FindBy(xpath = "//android.widget.ImageView[@content-desc=\"Restaurants Endpoint Image\"]")
    WebElement restaurantEntryPoint;

    @FindBy(xpath = "//android.widget.TextView[@text='Closed']")
    List<WebElement> restaurantClosedStatus;

    @FindBy(xpath = "//android.widget.TextView[@text='Busy']")
    List<WebElement> restaurantBusyStatus;

    String categoryFilterIdSelector = "homeCategoryCard[%s]";

    String categoryFilterHeaderIdSelector ="homeCategoryHeader[%s]";

    String restaurantCardId = "homeRestaurantCard[%s]";

    String restaurantCardNameId = "homeRestaurantName[%s]";

    String ratingElementId = "homeRestaurantRating[%s]";

    String homeRestaurantDeliveryTimeId = "homeRestaurantDeliveryTime[%s]";

    String homeRestaurantDeliveryFeesId = "homeRestaurantDeliveryFee[%s]";

    String homeRestaurantStatusXpath = "";

    String homeRestaurantClosedStatus = "//android.view.View[@resource-id=\"homeRestaurantClosedStatus[%s]\"]";

    String restaurantsScrollableContainer= "homeRestaurantScrollViewContainer";

    String restaurantReviewCountId = "homeRestaurantRating[%s]";

    String categoryFilterScrollableContainer = "homeCategoryContainer";
    public void pressBackBtn() {
        wait.until(ExpectedConditions.visibilityOf(homeBackBtn)).click();
    }

    String categoryFilterNameIdSelector = "homeCategoryName[%s]";

    public void pressRestaurantsEntryPoint() {
        wait.until(ExpectedConditions.visibilityOf(restaurantEntryPoint))
                .click();
    }

    public boolean isRestaurantHomeScreenDisplayed() {
        return isElementDisplayed(allRestaurantsTitle);
    }

    public boolean isBackToGroceryBtnDisplayed() {
        return isElementDisplayed(homeBackBtn);
    }

    public boolean isSearchDisplayed() {
        return isElementDisplayed(searchBtn);
    }

    public boolean isCartDisplayed() {
        return isElementDisplayed(cartIcon);
    }

    public boolean isCategoryFilterRowDisplayed() {
        return isElementDisplayed(getFilterScrollableContainer());
    }

    public boolean isCategoryFilterDisplayed(String categoryId){
        return isElementDisplayed(getCategoryFilterUiElement(categoryId));
    }

    public boolean isCategoryFilterHeaderDisplayed(String categoryId){
        return isElementDisplayed(getCategoryFilterHeaderUiElement(categoryId));
    }

    public boolean isRestaurantCardDisplayed(String restaurantId) {
        return isElementDisplayed(getRestaurantCardUiElement(restaurantId));
    }

    public boolean isRestaurantNameDisplayed(String restaurantId) {
        return isElementDisplayed(getRestaurantNameUiElement(restaurantId));
    }

    public WebElement getRestaurantNameUiElement(String restaurantId){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(By.id(
                getRestaurantNameIdSelector(restaurantId))));
    }

    public String getRestaurantNameIdSelector(String categoryId){
        return String.format(restaurantCardNameId,categoryId);
    }

    //Check here if at least one restaurant is shown.
    public boolean areRestaurantsListed(String restaurantId) {
        return isElementDisplayed(getRestaurantCardUiElement(restaurantId));
    }

    public void pressCartIcon() {
        wait.until(ExpectedConditions.visibilityOf(cartIcon))
                .click();
    }

    public void pressCategoryFilterById(String categoryId){
        getCategoryFilterUiElement(categoryId)
                .click();
        wait.until(ExpectedConditions.visibilityOfElementLocated(By.id(getCategoryFilterHeaderIdSelector(categoryId))));
    }

    public void deselectCategoryFilterById(String categoryId){
        getCategoryFilterUiElement(categoryId)
                .click();
        wait.until(ExpectedConditions.invisibilityOfElementLocated(By.id(getCategoryFilterHeaderIdSelector(categoryId))));
    }

    public void pressRestaurantCardById(String restaurantId){
        getRestaurantCardUiElement(restaurantId)
                .click();
        wait.until(ExpectedConditions.invisibilityOfElementLocated(By.id(
                getRestaurantCardIdSelector(restaurantId))));
    }

    public String getRestaurantName(String RestaurantId) {
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.id(String.format(restaurantCardNameId, RestaurantId))))
                .getText();
    }

    public String getRestaurantDeliveryTime(String RestaurantId) {
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.id(String.format(homeRestaurantDeliveryTimeId, RestaurantId))))
                .getText();
    }

    public String getRestaurantDeliveryFees(String RestaurantId) {
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.id(String.format(homeRestaurantDeliveryFeesId, RestaurantId))))
                .getText();
    }

    public String getRestaurantStatus(String RestaurantId) {
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                        By.xpath(String.format(homeRestaurantStatusXpath, RestaurantId))))
                .getText();
    }

    public String getRestaurantClosedStatus(String RestaurantId) {
        String dynamicXpath = String.format(homeRestaurantClosedStatus, RestaurantId);
        WebElement restaurantCardStatus = wait.until(ExpectedConditions.visibilityOfElementLocated(By.xpath(dynamicXpath)));
        return restaurantCardStatus.getText();
    }

    public boolean isCategoryFilterRowScrollable(){
        try {
            return  getFilterScrollableContainer().getDomAttribute("scrollable")
                    .equalsIgnoreCase("true");
        } catch (Exception e){
            return false;
        }
    }

    public String extractCategoryHeaderName(String categoryId){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.id(getCategoryFilterHeaderIdSelector(categoryId)))).getText();
    }

    public String extractCategoryFilterName(String categoryId){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.id(getCategoryFilterNameIdSelector(categoryId)))).getText();
    }

    public String getCategoryFilterIdSelector(String categoryId){
        return String.format(categoryFilterIdSelector,categoryId);
    }

    public String getCategoryFilterNameIdSelector(String categoryId){
        return String.format(categoryFilterNameIdSelector,categoryId);
    }

    public WebElement getCategoryFilterUiElement(String categoryId){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(By.id(
                getCategoryFilterIdSelector(categoryId))));
    }

    public String getCategoryFilterHeaderIdSelector(String categoryId){
        return String.format(categoryFilterHeaderIdSelector,categoryId);
    }

    public WebElement getCategoryFilterHeaderUiElement(String categoryId){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(By.id(
                getCategoryFilterHeaderIdSelector(categoryId))));
    }

    public boolean isCategoryFilterNameDisplayed(String categoryId) {
        return isElementDisplayed(getCategoryFilterNameUiElement(categoryId));
    }

    public WebElement getCategoryFilterNameUiElement(String categoryId){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(By.id(
                getCategoryFilterNameIdSelector(categoryId))));
    }

    public WebElement getRestaurantsScrollableContainer() {
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(By.id(restaurantsScrollableContainer)));
        } catch (Exception e) {
            return null;
        }
    }

    public WebElement getFilterScrollableContainer() {
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(By.id(categoryFilterScrollableContainer)));
        } catch (Exception e) {
            return null;
        }
    }

    public String getRestaurantCardIdSelector(String restaurantId){
        return String.format(restaurantCardId,restaurantId);
    }

    public WebElement getRestaurantCardUiElement(String restaurantId){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(By.id(
                getRestaurantCardIdSelector(restaurantId))));
    }

    public List<WebElement> getAllVisibleRestaurantCards() {
        try {
            List<WebElement> cards = androidDriver.findElements(By.xpath("//*[contains(@resource-id, 'homeRestaurantCard[')]"));
            logger.info("🧩 Visible card elements found: " + cards.size());

            for (WebElement card : cards) {
                String rid = card.getAttribute("resource-id");
                logger.info("🆔 Card resource-id: " + rid);
            }

            return cards;
        } catch (Exception e) {
            logger.error("❌ Failed to fetch visible restaurant cards: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    public List<WebElement> getAllVisibleCategoryCards() {
        try {
            // Match the homeCategoryCard[*] pattern
            return androidDriver.findElements(By.xpath("//*[contains(@resource-id, 'homeCategoryCard[')]"));
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    public void captureCurrentlyVisibleRestaurants(Set<String> discoveredIds) {

        try {
            // Get all restaurant cards currently visible in viewport
            List<WebElement> visibleCards = getAllVisibleRestaurantCards();

            for (WebElement card : visibleCards) {
                try {
                    String restaurantId = extractRestaurantIdFromCard(card);
                    if (restaurantId != null && !restaurantId.isEmpty()) {
                        discoveredIds.add(restaurantId);
                    }
                } catch (Exception e) {
                    // Continue processing other cards if one fails
                }
            }
        } catch (Exception e) {
            // Don't fail the test if capture fails, just log
            logger.error("Could not capture visible restaurants: {}", e.getMessage());
        }
    }

    private String extractRestaurantIdFromCard(WebElement card) {
        try {
            // Get the resource-id attribute
            String resourceId = card.getAttribute("resource-id");

            // Pattern: homeRestaurantCard[123] -> "123"
            if (resourceId != null && resourceId.contains("homeRestaurantCard[")) {
                int startIndex = resourceId.indexOf("homeRestaurantCard[") + "homeRestaurantCard[".length();
                int endIndex = resourceId.indexOf("]", startIndex);

                if (startIndex > 0 && endIndex > startIndex) {
                    return resourceId.substring(startIndex, endIndex);
                }
            }

            return null;
        } catch (Exception e) {
            return null;
        }
    }

    public void captureCurrentlyVisibleCategories(Set<String> discoveredIds) {

        try {
            // Get all category cards currently visible in viewport
            List<WebElement> visibleCards = getAllVisibleCategoryCards();

            for (WebElement card : visibleCards) {
                try {
                    String categoryId = extractCategoryIdFromCard(card);
                    if (categoryId != null && !categoryId.isEmpty()) {
                        discoveredIds.add(categoryId);
                    }
                } catch (Exception e) {
                    // Continue processing other cards if one fails
                }
            }
        } catch (Exception e) {
            // Don't fail the test if capture fails, just log
            logger.error("Could not capture visible categories: {}", e.getMessage());
        }
    }

    private String extractCategoryIdFromCard(WebElement card) {
        try {
            // Get the resource-id attribute
            String resourceId = card.getAttribute("resource-id");

            // Pattern: homeCategoryCard[123] -> "123"
            if (resourceId != null && resourceId.contains("homeCategoryCard[")) {
                int startIndex = resourceId.indexOf("homeCategoryCard[") + "homeCategoryCard[".length();
                int endIndex = resourceId.indexOf("]", startIndex);

                if (startIndex > 0 && endIndex > startIndex) {
                    return resourceId.substring(startIndex, endIndex);
                }
            }

            return null;
        } catch (Exception e) {
            return null;
        }
    }

    public String getRestaurantClosedStatusSelector() {
        return String.format(String.valueOf(restaurantClosedStatus));
    }

    public String getRestaurantBusyStatusSelector(String restaurantId) {
        return String.format(homeRestaurantClosedStatus, restaurantId);
    }

    public boolean isRestaurantRatingDisplayed(String restaurantId) {
        try {
            String ratingXpath = String.format(ratingElementId, restaurantId);
            return isElementDisplayed(androidDriver.findElement(By.xpath(ratingXpath)));
        } catch (Exception e) {
            return false;
        }
    }

    public String getRestaurantRating(String restaurantId) {
        try {
            String ratingXpath = String.format(ratingElementId, restaurantId);
            return wait.until(ExpectedConditions.visibilityOfElementLocated(By.xpath(ratingXpath))).getText();
        } catch (Exception e) {
            return null;
        }
    }

    public int getRestaurantReviewCount(String restaurantId) {
        try {
            String reviewCountXpath = String.format(restaurantReviewCountId, restaurantId);
            String reviewCountText = wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.id(reviewCountXpath))).getText();

            // Extract numeric value from text (e.g., "123 reviews" -> 123)
            String numericPart = reviewCountText.replaceAll("[^0-9]", "");
            return numericPart.isEmpty() ? 0 : Integer.parseInt(numericPart);
        } catch (Exception e) {
            return 0;
        }
    }

    public boolean checkIfRatingIsDisplayed(String restaurantId) {
        try {
            WebElement ratingElement = wait.until(ExpectedConditions.presenceOfElementLocated(
                    By.id(String.format(ratingElementId, restaurantId))
            ));
            return ratingElement.isDisplayed();
        } catch (TimeoutException e) {
            return false;
        }
    }

    public String getUnifiedRestaurantStatusFromUI() {
        try {
            // Check if Closed status element is present and visible
            if (!restaurantClosedStatus.isEmpty() && restaurantClosedStatus.get(0).isDisplayed()) {
                return "closed";
            }

            // Check if Busy status element is present and visible
            if (!restaurantBusyStatus.isEmpty() && restaurantBusyStatus.get(0).isDisplayed()) {
                return "busy";
            }

            // If neither Closed nor Busy found, assume Open
            return "open";
        } catch (NoSuchElementException | IndexOutOfBoundsException e) {
            // No elements found, default to "open" or handle error gracefully
            return "open";
        }
    }

    public String verifyRestaurantStatus(String operatingStatus) {
        String uiStatus = getUnifiedRestaurantStatusFromUI();  // unified status from UI

        operatingStatus = operatingStatus.toLowerCase();

        switch (operatingStatus) {
            case "CLOSED":
                logger.info("Status is CLOSED in API, UI shows: " + uiStatus);
                // You can choose to assert here or just return and let test assert
                break;
            case "BUSY":
                logger.info("Status is BUSY in API. UI shows: " + uiStatus );
                break;
            case "OPEN":
                logger.info("Status is OPEN in API. UI shows: " + uiStatus);
                break;
            default:
                logger.info("Unknown API status '" + operatingStatus + "'. UI shows: " + uiStatus);
        }
        return uiStatus;  // return unified UI status to compare in test
    }
    public Set<String> getCurrentlyVisibleRestaurants() {
        Set<String> visibleRestaurants = new HashSet<>();

        try {
            List<WebElement> visibleCards = getAllVisibleRestaurantCards();
            for (WebElement card : visibleCards) {
                String restaurantId = extractRestaurantIdFromCard(card);
                if (restaurantId != null && !restaurantId.isEmpty()) {
                    visibleRestaurants.add(restaurantId);
                }
            }
        } catch (Exception e) {
            logger.warn("Error getting currently visible restaurants: {}", e.getMessage());
        }

        return visibleRestaurants;
    }

    public boolean isDeliveryFeeDisplayed(String restaurantId) {
        try {
            WebElement deliveryFees = wait.until(ExpectedConditions.presenceOfElementLocated(
                    By.id(String.format(homeRestaurantDeliveryFeesId, restaurantId))
            ));
            logger.info("Delivery Fees is displayed and shown as :" + deliveryFees.getText());
            return deliveryFees.isDisplayed();
        } catch (TimeoutException e) {
            return false;
        }
    }
}
